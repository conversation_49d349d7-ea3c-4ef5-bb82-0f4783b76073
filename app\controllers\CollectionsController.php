<?php
namespace App\Controllers;
use App\Core\Controller; use App\Core\DB;

class CollectionsController extends Controller
{
    public function index(): void
    {
        $rows = DB::pdo()->query('SELECT id, title, slug, description, image_url FROM collections ORDER BY title')->fetchAll();
        $this->view('collections/index', ['collections'=>$rows]);
    }

    public function show(array $params): void
    {
        $pdo = DB::pdo();
        $st = $pdo->prepare('SELECT * FROM collections WHERE slug=?'); $st->execute([$params['slug']]);
        $c = $st->fetch(); if(!$c){ http_response_code(404); $this->view('errors/404'); return; }

        // Get first page of products (24 per page for better mobile experience)
        $pageSize = 24;
        $ps = $pdo->prepare('SELECT p.*, (SELECT url FROM product_images WHERE product_id=p.id ORDER BY sort_order LIMIT 1) AS image_url FROM products p WHERE status="active" AND collection_id = ? ORDER BY created_at DESC LIMIT ?');
        $ps->execute([$c['id'], $pageSize]);
        $products = $ps->fetchAll();

        // Get total count for infinite scroll
        $countStmt = $pdo->prepare('SELECT COUNT(*) FROM products WHERE status="active" AND collection_id = ?');
        $countStmt->execute([$c['id']]);
        $totalCount = (int)$countStmt->fetchColumn();

        $this->view('collections/show', [
            'collection' => $c,
            'products' => $products,
            'totalCount' => $totalCount,
            'pageSize' => $pageSize
        ]);
    }

    public function loadMore(array $params): void
    {
        $pdo = DB::pdo();
        $st = $pdo->prepare('SELECT * FROM collections WHERE slug=?'); $st->execute([$params['slug']]);
        $c = $st->fetch(); if(!$c){ $this->json(['error' => 'Collection not found'], 404); return; }

        $page = max(1, (int)($_GET['page'] ?? 1));
        $pageSize = 24;
        $offset = ($page - 1) * $pageSize;

        $ps = $pdo->prepare('SELECT p.*, (SELECT url FROM product_images WHERE product_id=p.id ORDER BY sort_order LIMIT 1) AS image_url FROM products p WHERE status="active" AND collection_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?');
        $ps->execute([$c['id'], $pageSize, $offset]);
        $products = $ps->fetchAll();

        ob_start();
        foreach ($products as $p) {
            include BASE_PATH . '/app/views/products/_card.php';
        }
        $html = ob_get_clean();

        $this->json([
            'html' => $html,
            'hasMore' => count($products) === $pageSize
        ]);
    }
}

