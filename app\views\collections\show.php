<?php use function App\Core\e; ?>
<h1 class="h4 mb-3"><?= e($collection['title']) ?></h1>
<?php if (!empty($collection['image_url'])): ?>
  <div class="ratio ratio-21x9 mb-3" style="background:#f8f9fa; overflow:hidden; border-radius:.5rem">
    <img src="<?= e($collection['image_url']) ?>" class="w-100 h-100 object-fit-cover" alt="<?= e($collection['title']) ?>"/>
  </div>
<?php endif; ?>
<?php if (!empty($collection['description'])): ?>
  <p class="text-muted"><?= nl2br(e($collection['description'])) ?></p>
<?php endif; ?>

<?php if (empty($products)): ?>
  <div class="text-center py-5">
    <i class="bi bi-box display-1 text-muted"></i>
    <h3 class="h4 mt-3">No products in this collection</h3>
    <p class="text-muted">Check back later for new products.</p>
  </div>
<?php else: ?>
  <div class="row g-3" id="productGrid">
    <?php foreach ($products as $p) { include BASE_PATH . '/app/views/products/_card.php'; } ?>
  </div>

  <?php if ($totalCount > count($products)): ?>
    <div class="text-center py-4" id="loadMoreWrap">
      <div class="spinner-border" role="status" id="loader" style="display:none"></div>
    </div>

    <script>
      window.COLLECTION_SCROLL = {
        page: 1,
        busy: false,
        hasMore: <?= $totalCount > count($products) ? 'true' : 'false' ?>,
        slug: '<?= e($collection['slug']) ?>'
      };
    </script>
  <?php endif; ?>
<?php endif; ?>

