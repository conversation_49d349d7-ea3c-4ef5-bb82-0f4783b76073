body{font-feature-settings:"ss01" 1;}
.hero{background:linear-gradient(135deg,#f8f9fa 0%,#e9ecef 100%)}
.card img{transition:transform .3s ease}
.card:hover img{transform:scale(1.03)}
.card.hover-lift{transition:box-shadow .2s ease, transform .2s ease}
.card.hover-lift:hover{box-shadow:0 1rem 2rem rgba(0,0,0,.08); transform:translateY(-2px)}
.btn-dark{border-radius:8px}
.navbar-brand{letter-spacing:.5px}
#cartDrawer .offcanvas-body{min-height:200px}
#cartDrawer{width:400px; max-width:90vw}
#cartDrawer .list-group-item{padding:0.75rem; border-left:0; border-right:0}
#cartDrawer .input-group{width:100px}

/* Mobile Responsiveness */
@media (max-width: 767.98px){
  /* PDP sticky CTA on mobile */
  .sticky-cta{position:sticky; bottom:0; left:0; right:0; background:#fff; border-top:1px solid #e9ecef; padding:.75rem; z-index:1029}

  /* Mobile navigation improvements */
  .navbar-brand{font-size:1.1rem}
  .container{padding-left:1rem; padding-right:1rem}

  /* Mobile card improvements */
  .card{margin-bottom:1rem}
  .card-body{padding:1rem}
  .card-title{font-size:1rem}

  /* Mobile table improvements */
  .table-responsive{font-size:0.875rem; border:0}
  .table td, .table th{padding:0.5rem 0.25rem; border-top:1px solid #dee2e6}
  .btn-group-sm .btn{padding:0.25rem 0.5rem; font-size:0.75rem}

  /* Mobile form improvements */
  .form-control, .form-select{font-size:16px} /* Prevents zoom on iOS */
  .input-group{flex-wrap:nowrap}

  /* Mobile admin improvements */
  .admin-sidebar{position:static; width:100%; min-height:auto}
  .admin-content{padding:0.5rem}
  .admin-header{padding:0.5rem 0; flex-direction:column; align-items:flex-start}
  .admin-header .d-flex{width:100%; justify-content:space-between}

  /* Mobile product grid */
  .col-6{padding-left:0.5rem; padding-right:0.5rem}

  /* Mobile search */
  #mobileSearchResults, #desktopSearchResults{max-height:250px}
}

/* Trust badges */
.trust-badges .bi{font-size:1.25rem}

/* Product badges - prevent overlapping */
.badge {
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
}

/* Search results dropdowns */
#mobileSearchResults,
#desktopSearchResults {
  border-radius: 0 0 0.375rem 0.375rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border: 1px solid #dee2e6;
  background: white;
}

#mobileSearchResults a,
#desktopSearchResults a {
  transition: background-color 0.15s ease-in-out;
}

#mobileSearchResults a:hover,
#desktopSearchResults a:hover {
  background-color: #f8f9fa;
}

/* Mobile search results */
@media (max-width: 767.98px) {
  #mobileSearchResults {
    border-radius: 0 0 0.375rem 0.375rem;
  }
}


/* Skeleton loaders */
/* Modern polish */
.card{ border:0; border-radius:14px; }
.card-img-top{ border-top-left-radius:14px; border-top-right-radius:14px; object-fit:cover; }
.ratio.ratio-1x1{ border-radius:14px; }
.container{ max-width:1140px; }

.skeleton{ position:relative; overflow:hidden; background:#e9ecef; border-radius:.5rem; }
.skeleton::after{ content:""; position:absolute; inset:0; transform:translateX(-100%);
  background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,.6) 50%, rgba(255,255,255,0) 100%);
  animation: shimmer 1.2s infinite; }
@keyframes shimmer { 100%{ transform:translateX(100%);} }

/* Additional Mobile Improvements */
@media (max-width: 575.98px){
  /* Extra small devices */
  .container{max-width:100%; padding-left:0.75rem; padding-right:0.75rem}
  .card{border-radius:8px}
  .btn{padding:0.5rem 1rem; font-size:0.875rem}
  .btn-lg{padding:0.75rem 1.5rem; font-size:1rem}

  /* Mobile checkout improvements */
  .checkout-form .row{margin:0}
  .checkout-form .col-md-6{padding:0.25rem}

  /* Mobile cart improvements */
  #cartDrawer{width:100%; max-width:100vw}
  .offcanvas-body{padding:1rem}

  /* Mobile homepage improvements */
  .hero{padding:2rem 0}
  .hero h1{font-size:1.75rem}
  .hero p{font-size:1rem}

  /* Mobile product cards */
  .col-6{flex:0 0 50%; max-width:50%}
  .card-img-top{height:150px; object-fit:cover}

  /* Mobile admin dashboard */
  .admin-sidebar .nav-link{padding:0.5rem 0.75rem; font-size:0.875rem}
  .admin-header h1{font-size:1.25rem}
}

/* Tablet improvements */
@media (min-width: 576px) and (max-width: 991.98px){
  .container{max-width:540px}
  .card-img-top{height:200px}
  .admin-sidebar{width:250px}
}
